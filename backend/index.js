const express = require('express');
const cors = require('cors');
const path = require('path');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyTokenMiddleware = require('./lib/middleware/verifyToken');

// Handle routes
const ApiRoutes = require('./lib/routes/api');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').createServer(app);
global.io = require('socket.io')(server);

// Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));

// Disable caching for static files - DISABLED (frontend container serves React app)
// app.use(express.static('public', {
//   setHeaders: (res, path) => {
//     res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
//     res.setHeader('Pragma', 'no-cache');
//     res.setHeader('Expires', '0');
//   }
// }));

// Force serve the correct React app - DISABLED (frontend container serves React app)
// app.get('/', (req, res) => {
//   res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
//   res.setHeader('Pragma', 'no-cache');
//   res.setHeader('Expires', '0');
//   res.sendFile(path.join(__dirname, 'public', 'index.html'));
// });

// Test route to serve React app
app.get('/app', (req, res) => {
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.sendFile(path.join(__dirname, 'public', 'react-app.html'));
});

// New test route
app.get('/newapp', (req, res) => {
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.sendFile(path.join(__dirname, 'public', 'react-app.html'));
});

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  next();
});

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// Import upload middleware
const { uploadSingle, handleUploadError } = require('./lib/middleware/upload');

// API Routes - Example routes for the template
try {
  console.log('Registering routes...');
  declareRoute('post', '/auth/login', [], ApiRoutes.auth.login);
  declareRoute('post', '/auth/register', [], ApiRoutes.auth.register);
  declareRoute('post', '/user/profile', [tokenToUserMiddleware], ApiRoutes.user.profile);
  declareRoute('post', '/user/update', [tokenToUserMiddleware], ApiRoutes.user.update);

  // File management routes
  declareRoute('post', '/files/upload', [uploadSingle, handleUploadError], ApiRoutes.files.upload);
  declareRoute('get', '/files/download/:fileId', [], ApiRoutes.files.download);
  declareRoute('get', '/files/preview/:fileId', [], ApiRoutes.files.preview);
  console.log('About to register browse route...');
  declareRoute('get', '/browse/:folderId?', [], ApiRoutes.browse);
  console.log('Browse route registered successfully');

  // Folder routes
  declareRoute('post', '/folders/create', [], ApiRoutes.folders.create);
  declareRoute('post', '/folders', [], ApiRoutes.folders.create); // Alias for frontend compatibility
  console.log('Folder routes registered successfully');

  // Test endpoint for iframe debugging
  app.get('/api/v1.0/test-iframe', (req, res) => {
    res.setHeader('Content-Type', 'text/html');
    res.setHeader('Access-Control-Allow-Origin', '*');
    // Don't set X-Frame-Options to allow iframe embedding
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Test Iframe Content</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
          h1 { color: #333; }
        </style>
      </head>
      <body>
        <h1>✅ Iframe Test Successful!</h1>
        <p>This content is loaded inside an iframe.</p>
        <p>Current time: ${new Date().toISOString()}</p>
        <p>If you can see this, iframe embedding is working correctly.</p>
      </body>
      </html>
    `);
  });

  console.log('All routes registered successfully');
} catch (error) {
  console.error('Error registering routes:', error);
}

// Simple folder creation endpoint with detailed logging
app.post('/api/v1.0/folders/create', async (req, res) => {
  console.log('=== FOLDER CREATE API CALLED ===');
  console.log('Request body:', req.body);

  try {
    const { folderName, parentId } = req.body;
    console.log('Extracted folderName:', folderName, 'parentId:', parentId);

    // Validate input
    console.log('Step 1: Validating input...');
    if (!folderName || typeof folderName !== 'string' || folderName.trim().length === 0) {
      console.log('Validation failed: Invalid folder name');
      return res.json({
        code: 400,
        message: 'Folder name is required and must be a non-empty string'
      });
    }

    // Validate folder name (no special characters)
    console.log('Step 2: Checking for invalid characters...');
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(folderName.trim())) {
      console.log('Validation failed: Invalid characters in folder name');
      return res.json({
        code: 400,
        message: 'Folder name contains invalid characters'
      });
    }

    console.log('Step 3: Loading FolderModel...');
    const FolderModel = require('./lib/models/folder');
    console.log('FolderModel loaded successfully');

    // Check if folder already exists
    console.log('Step 4: Checking for existing folder...');
    console.log('Query params:', {
      folderName: folderName.trim(),
      parentId: parentId || null,
      isDeleted: false
    });

    const existingFolder = await FolderModel.findOne({
      folderName: folderName.trim(),
      parentId: parentId || null,
      isDeleted: false
    }).lean();

    console.log('Step 4 completed. Existing folder:', existingFolder);

    if (existingFolder) {
      console.log('Folder already exists, returning error');
      return res.json({
        code: 400,
        message: 'A folder with this name already exists in the current location'
      });
    }

    // Create new folder
    console.log('Step 5: Creating new folder...');
    const newFolder = new FolderModel({
      folderName: folderName.trim(),
      parentId: parentId || null,
      createdAt: new Date()
    });
    console.log('New folder object created:', newFolder);

    console.log('Step 6: Saving folder to database...');
    const savedFolder = await newFolder.save();
    console.log('Folder saved successfully:', savedFolder);

    console.log('Step 7: Sending response...');
    const response = {
      code: 200,
      data: {
        id: savedFolder._id,
        folderName: savedFolder.folderName,
        parentId: savedFolder.parentId,
        createdAt: savedFolder.createdAt
      },
      message: 'Folder created successfully'
    };
    console.log('Response object:', response);

    res.json(response);
    console.log('Response sent successfully');

  } catch (error) {
    console.error('=== ERROR IN FOLDER CREATE ===');
    console.error('Error details:', error);
    console.error('Error stack:', error.stack);
    res.json({
      code: 500,
      message: 'Internal server error'
    });
  }

  console.log('=== FOLDER CREATE API FINISHED ===');
});
declareRoute('put', '/items/:id/rename', [], ApiRoutes.items.rename);
declareRoute('delete', '/items/:id', [], ApiRoutes.items.delete);
declareRoute('get', '/search', [], ApiRoutes.search);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

// Simple test route to check if routing works
app.get('/api/v1.0/test', (req, res) => {
  res.json({ message: 'Test route works!', timestamp: new Date() });
});

// Test folder creation endpoint
app.post('/api/v1.0/test/folder', async (req, res) => {
  try {
    const { folderName } = req.body;

    if (!folderName) {
      return res.json({ error: 'folderName is required' });
    }

    const FolderModel = require('./lib/models/folder');
    const newFolder = new FolderModel({
      folderName: folderName,
      parentId: null,
      createdAt: new Date()
    });

    const savedFolder = await newFolder.save();

    res.json({
      success: true,
      data: {
        id: savedFolder._id,
        folderName: savedFolder.folderName,
        parentId: savedFolder.parentId,
        createdAt: savedFolder.createdAt
      }
    });
  } catch (error) {
    res.json({ error: error.message });
  }
});

const port = _.get(config, 'port', 3000);
console.log('About to start server on port:', port);
console.log('Express app routes:');
app._router.stack.forEach((middleware, index) => {
  if (middleware.route) {
    console.log(`${index}: ${middleware.route.stack[0].method.toUpperCase()} ${middleware.route.path}`);
  } else if (middleware.name === 'router') {
    console.log(`${index}: Router middleware`);
  } else {
    console.log(`${index}: ${middleware.name} middleware`);
  }
});

server.listen(port, '0.0.0.0', () => {
  logger.logInfo('Server listening at port:', port);
  console.log('Server started successfully!');
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
