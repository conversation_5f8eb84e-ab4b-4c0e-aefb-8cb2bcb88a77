const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const cacheService = require('../../../services/cache');
const async = require('async');
const _ = require('lodash');
const config = require('config');

module.exports = async (req, res) => {
  // Check if request is coming from iframe preview (prevent infinite loops)
  const referer = req.headers.referer;
  if (referer && referer.includes('/files/preview/')) {
    console.log('🚫 BLOCKED: Request from iframe preview detected:', referer);
    return res.status(403).json({
      code: CONSTANTS.CODE.FORBIDDEN,
      message: 'Access denied from iframe context'
    });
  }

  const validateParams = (next) => {
    try {
      const folderId = req.params.folderId || null;

      // Validate folder exists if folderId is provided
      if (folderId) {
        const FolderModel = require('../../../models/folder');
        FolderModel.findOne({
          _id: folderId,
          isDeleted: false
        }).lean()
        .then(folder => {
          if (!folder) {
            return next({
              code: CONSTANTS.CODE.NOT_FOUND,
              message: 'Folder not found'
            });
          }
          next(null, folderId);
        })
        .catch(error => {
          global.logger.logError(['browse/validateParams', error], __dirname);
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        });
      } else {
        next(null, folderId);
      }
    } catch (error) {
      global.logger.logError(['browse/validateParams', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const checkCache = (folderId, next) => {
    // Temporarily disable cache
    next(null, folderId, null);
  };

  const getFolderContent = (folderId, cachedData, next) => {
    try {
      if (cachedData) {
        return next(null, cachedData);
      }

      // Use mock data if enabled
      if (config.has('mockData') && config.get('mockData')) {
        const mockDataService = require('../../../services/mockData');
        mockDataService.getFolderContent(folderId)
          .then(result => {
            next(null, result);
          })
          .catch(error => {
            global.logger.logError(['browse/getFolderContent-mock', error], __dirname);
            next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          });
        return;
      }

      const FileModel = require('../../../models/file');
      const FolderModel = require('../../../models/folder');

      // Get folders and files in parallel
      Promise.all([
        FolderModel.find({
          parentId: folderId,
          isDeleted: false
        })
        .select('_id folderName createdAt parentId')
        .sort({ folderName: 1 })
        .lean(),

        FileModel.find({
          parentId: folderId,
          isDeleted: false
        })
        .select('_id originalFileName fileSize mimeType uploadDate parentId telegramFileId')
        .sort({ originalFileName: 1 })
        .lean()
      ])
      .then(([folders, files]) => {
        // Format response
        const result = {
          folders: folders.map(folder => ({
            id: folder._id,
            name: folder.folderName,
            type: 'folder',
            createdAt: folder.createdAt,
            parentId: folder.parentId
          })),
          files: files.map(file => ({
            id: file._id,
            name: file.originalFileName,
            type: 'file',
            size: file.fileSize,
            mimeType: file.mimeType,
            uploadDate: file.uploadDate,
            parentId: file.parentId,
            telegramFileId: file.telegramFileId
          }))
        };

        // Cache the result
        const cacheKey = cacheService.getFolderContentKey(folderId);
        cacheService.set(cacheKey, result, 900) // 15 minutes
          .then(() => {
            next(null, result);
          })
          .catch(() => {
            // Continue even if caching fails
            next(null, result);
          });
      })
      .catch(error => {
        global.logger.logError(['browse/getFolderContent', error], __dirname);
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      });
    } catch (error) {
      global.logger.logError(['browse/getFolderContent', error], __dirname);
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  async.waterfall([validateParams, checkCache, getFolderContent], (err, data) => {
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      };
    }

    const response = err || {
      code: CONSTANTS.CODE.SUCCESS,
      data: data,
      message: 'Folder content retrieved successfully'
    };

    res.json(response);
  });
};
