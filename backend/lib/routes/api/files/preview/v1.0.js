const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const telegramService = require('../../../../services/telegram');
const cacheService = require('../../../../services/cache');

module.exports = async (req, res) => {
  try {
    const fileId = req.params.fileId;
    console.log(`🎬 PREVIEW REQUEST: File ID: ${fileId}`);
    console.log(`🎬 PREVIEW REQUEST: Referer: ${req.headers.referer}`);
    console.log(`🎬 PREVIEW REQUEST: User-Agent: ${req.headers['user-agent']}`);

    if (!fileId) {
      return res.status(400).json({
        code: CONSTANTS.CODE.INVALID_PARAMS,
        message: 'File ID is required'
      });
    }

    const FileModel = require('../../../../models/file');
    const file = await FileModel.findOne({
      _id: fileId,
      isDeleted: false
    }).lean();

    if (!file) {
      console.log(`🎬 PREVIEW ERROR: File not found: ${fileId}`);
      return res.status(404).json({
        code: CONSTANTS.CODE.NOT_FOUND,
        message: 'File not found'
      });
    }

    console.log(`🎬 PREVIEW FILE INFO:`);
    console.log(`   - Name: ${file.originalFileName}`);
    console.log(`   - MIME Type: ${file.mimeType}`);
    console.log(`   - Size: ${file.fileSize}`);
    console.log(`   - Telegram File ID: ${file.telegramFileId}`);

    // Get file URL from Telegram
    const cacheKey = cacheService.getTelegramUrlKey(file.telegramFileId);
    let fileUrl = await cacheService.get(cacheKey);

    if (!fileUrl) {
      // Get fresh URL from Telegram
      fileUrl = await telegramService.getFileUrl(file.telegramFileId);

      // Cache URL for 30 minutes (Telegram URLs expire after ~1 hour)
      try {
        await cacheService.set(cacheKey, fileUrl, 1800);
      } catch (cacheError) {
        console.warn('Cache set failed:', cacheError.message);
      }
    }

    // Set response headers for inline viewing (not download)
    res.setHeader('Content-Type', file.mimeType);
    res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(file.originalFileName)}"`);
    res.setHeader('Content-Length', file.fileSize);

    // Add CORS headers for browser access
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    // Allow iframe embedding for preview - remove all frame restrictions
    // res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    // res.setHeader('Content-Security-Policy', "frame-ancestors 'self' http://localhost:3000 localhost:3000");

    // Cache headers for better performance
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.setHeader('ETag', `"${file._id}-${file.uploadDate}"`);

    // Handle range requests for video/audio files
    const range = req.headers.range;
    if (range && (file.mimeType.startsWith('video/') || file.mimeType.startsWith('audio/'))) {
      res.setHeader('Accept-Ranges', 'bytes');
    }

    // Get file stream from Telegram
    const fileStream = await telegramService.getFileStream(file.telegramFileId);

    // Pipe the stream to response
    fileStream.pipe(res);

    // Handle stream events
    fileStream.on('error', (error) => {
      console.error('Stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: 'Failed to preview file'
        });
      }
    });

    fileStream.on('end', () => {
      console.log(`File previewed: ${file.originalFileName}, ID: ${file._id}`);
    });

  } catch (error) {
    console.error('Preview error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: 'Failed to preview file'
      });
    }
  }
};
