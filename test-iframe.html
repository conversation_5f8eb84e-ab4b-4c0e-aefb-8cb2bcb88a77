<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Iframe Preview</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-container {
            margin: 20px 0;
            border: 1px solid #ccc;
            padding: 20px;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
        }
        .url-display {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>Test Iframe Preview</h1>

    <div class="test-container">
        <h2>Test 1: Direct PDF URL</h2>
        <div class="url-display">
            URL: http://localhost:3000/api/v1.0/files/preview/68748cef1902d207c3feb483
        </div>
        <iframe src="http://localhost:3000/api/v1.0/files/preview/68748cef1902d207c3feb483" title="PDF Test"></iframe>
    </div>

    <div class="test-container">
        <h2>Test 2: Simple HTML Content (Control Test)</h2>
        <div class="url-display">
            URL: http://localhost:3000/api/v1.0/test-iframe
        </div>
        <iframe src="http://localhost:3000/api/v1.0/test-iframe" title="HTML Test"></iframe>
    </div>

    <div class="test-container">
        <h2>Test 3: DOC File</h2>
        <div class="url-display">
            URL: http://localhost:3000/api/v1.0/files/preview/68748ce71902d207c3feb467
        </div>
        <iframe src="http://localhost:3000/api/v1.0/files/preview/68748ce71902d207c3feb467" title="DOC Test"></iframe>
    </div>

    <script>
        // Add iframe load event listeners for debugging
        document.querySelectorAll('iframe').forEach((iframe, index) => {
            iframe.addEventListener('load', () => {
                console.log(`Iframe ${index + 1} loaded successfully`);
            });

            iframe.addEventListener('error', (e) => {
                console.error(`Iframe ${index + 1} failed to load:`, e);
            });
        });
    </script>
</body>
</html>
